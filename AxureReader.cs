using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using Axure.Document;
using Axure.Document.Widgets;

namespace AxureReader
{
    class Program
    {
        static void Main(string[] args)
        {
            string rpFilePath = "完整性(3)_recovered.rp";
            
            if (!File.Exists(rpFilePath))
            {
                Console.WriteLine($"文件不存在: {rpFilePath}");
                return;
            }

            try
            {
                Console.WriteLine($"正在读取Axure文件: {rpFilePath}");
                
                // 加载Axure文档
                var doc = RPDocument.Load(rpFilePath);
                
                Console.WriteLine("✓ 文件加载成功!");
                
                // 分析文档结构
                AnalyzeDocument(doc);
                
                // 导出为XML
                ExportToXml(doc, "axure_export.xml");
                
                // 导出页面截图
                ExportScreenshots(doc, "screenshots");
                
                Console.WriteLine("分析完成!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        static void AnalyzeDocument(RPDocument doc)
        {
            Console.WriteLine("\n=== 文档分析 ===");
            
            // 分析站点地图
            Console.WriteLine($"站点地图根节点数量: {doc.Sitemap.RootNodes.Count()}");
            AnalyzeTreeMap("页面", doc.Sitemap);
            
            // 分析母版地图
            Console.WriteLine($"母版地图根节点数量: {doc.Mastermap.RootNodes.Count()}");
            AnalyzeTreeMap("母版", doc.Mastermap);
            
            // 统计页面
            var pageHandles = GetAllPackageHandles(doc.Sitemap.RootNodes).ToList();
            Console.WriteLine($"总页面数: {pageHandles.Count}");
            
            // 统计母版
            var masterHandles = GetAllPackageHandles(doc.Mastermap.RootNodes).ToList();
            Console.WriteLine($"总母版数: {masterHandles.Count}");
            
            // 分析页面详情
            AnalyzePages(doc, pageHandles.Take(5)); // 只分析前5个页面
            
            // 分析母版详情
            AnalyzeMasters(doc, masterHandles.Take(3)); // 只分析前3个母版
        }

        static void AnalyzeTreeMap(string type, RPTreeMap treeMap)
        {
            Console.WriteLine($"\n--- {type}结构 ---");
            foreach (var rootNode in treeMap.RootNodes)
            {
                AnalyzeTreeMapNode(rootNode, 0);
            }
        }

        static void AnalyzeTreeMapNode(RPTreeMapNode node, int depth)
        {
            string indent = new string(' ', depth * 2);
            
            if (node.NodeType == RPTreeMapNodeType.Folder)
            {
                Console.WriteLine($"{indent}📁 文件夹: {node.NodeValue}");
            }
            else if (node.NodeType == RPTreeMapNodeType.PackageHandle)
            {
                var handle = (RPPackageHandle)node.NodeValue;
                Console.WriteLine($"{indent}📄 包: {handle}");
            }
            
            foreach (var child in node.ChildNodes)
            {
                AnalyzeTreeMapNode(child, depth + 1);
            }
        }

        static void AnalyzePages(RPDocument doc, IEnumerable<RPPackageHandle> pageHandles)
        {
            Console.WriteLine("\n--- 页面详情 ---");
            
            foreach (var handle in pageHandles)
            {
                try
                {
                    var page = (RPPage)doc.LoadPackage(handle);
                    if (page == null) continue;
                    
                    var packageInfo = doc.GetPackageInfo(handle);
                    Console.WriteLine($"\n页面: {packageInfo.PackageName}");
                    
                    if (page.Diagram != null)
                    {
                        Console.WriteLine($"  组件数量: {page.Diagram.Widgets.Count()}");
                        
                        // 分析组件类型
                        var widgetTypes = page.Diagram.Widgets
                            .GroupBy(w => w.GetType().Name)
                            .OrderByDescending(g => g.Count());
                            
                        foreach (var group in widgetTypes.Take(5))
                        {
                            Console.WriteLine($"    {group.Key}: {group.Count()}个");
                        }
                        
                        // 显示一些组件的文本内容
                        var textWidgets = page.Diagram.Widgets
                            .OfType<RPTextWidget>()
                            .Where(w => !string.IsNullOrEmpty(w.Text))
                            .Take(3);
                            
                        foreach (var widget in textWidgets)
                        {
                            string text = widget.Text.Length > 50 ? 
                                widget.Text.Substring(0, 50) + "..." : widget.Text;
                            Console.WriteLine($"    文本: \"{text}\"");
                        }
                    }
                    
                    if (page.HasInteraction)
                    {
                        Console.WriteLine($"  交互事件数量: {page.Interaction.Events.Count()}");
                    }
                    
                    page.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  分析页面时出错: {ex.Message}");
                }
            }
        }

        static void AnalyzeMasters(RPDocument doc, IEnumerable<RPPackageHandle> masterHandles)
        {
            Console.WriteLine("\n--- 母版详情 ---");
            
            foreach (var handle in masterHandles)
            {
                try
                {
                    var master = (RPMaster)doc.LoadPackage(handle);
                    if (master == null) continue;
                    
                    var packageInfo = doc.GetPackageInfo(handle);
                    Console.WriteLine($"\n母版: {packageInfo.PackageName}");
                    
                    if (master.Diagram != null)
                    {
                        Console.WriteLine($"  组件数量: {master.Diagram.Widgets.Count()}");
                    }
                    
                    master.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  分析母版时出错: {ex.Message}");
                }
            }
        }

        static void ExportToXml(RPDocument doc, string xmlPath)
        {
            Console.WriteLine($"\n正在导出XML到: {xmlPath}");
            
            try
            {
                using (var writer = new XmlTextWriter(xmlPath, Encoding.UTF8))
                {
                    writer.Formatting = Formatting.Indented;
                    writer.Indentation = 2;
                    
                    var generator = new SimpleXmlGenerator(doc, writer);
                    generator.Generate();
                }
                
                Console.WriteLine("✓ XML导出成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"XML导出失败: {ex.Message}");
            }
        }

        static void ExportScreenshots(RPDocument doc, string screenshotDir)
        {
            Console.WriteLine($"\n正在导出截图到: {screenshotDir}");
            
            try
            {
                if (!Directory.Exists(screenshotDir))
                {
                    Directory.CreateDirectory(screenshotDir);
                }
                
                var pageHandles = GetAllPackageHandles(doc.Sitemap.RootNodes).Take(5); // 只导出前5个页面
                int pageNumber = 1;
                
                foreach (var handle in pageHandles)
                {
                    try
                    {
                        var page = (RPPage)doc.LoadPackage(handle);
                        if (page?.Diagram == null) continue;
                        
                        var packageInfo = doc.GetPackageInfo(handle);
                        string filename = $"page_{pageNumber:000}_{SanitizeFileName(packageInfo.PackageName)}.jpg";
                        string fullPath = Path.Combine(screenshotDir, filename);
                        
                        using (var stream = File.Create(fullPath))
                        {
                            page.Diagram.RenderScreenshot(stream, RPImageFormat.Jpeg);
                        }
                        
                        Console.WriteLine($"  ✓ 导出: {filename}");
                        page.Dispose();
                        pageNumber++;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  导出页面截图失败: {ex.Message}");
                    }
                }
                
                Console.WriteLine("✓ 截图导出完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"截图导出失败: {ex.Message}");
            }
        }

        static string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        static IEnumerable<RPPackageHandle> GetAllPackageHandles(IEnumerable<RPTreeMapNode> nodes)
        {
            foreach (var node in nodes)
            {
                if (node.NodeValue is RPPackageHandle handle)
                {
                    yield return handle;
                }
                
                foreach (var subHandle in GetAllPackageHandles(node.ChildNodes))
                {
                    yield return subHandle;
                }
            }
        }
    }

    // 简化的XML生成器
    public class SimpleXmlGenerator
    {
        private readonly RPDocument _document;
        private readonly XmlWriter _writer;

        public SimpleXmlGenerator(RPDocument document, XmlWriter writer)
        {
            _document = document;
            _writer = writer;
        }

        public void Generate()
        {
            _writer.WriteStartDocument();
            _writer.WriteStartElement("AxureDocument");
            
            // 写入基本信息
            _writer.WriteElementString("ExportTime", DateTime.Now.ToString());
            
            // 写入页面信息
            _writer.WriteStartElement("Pages");
            var pageHandles = GetAllPackageHandles(_document.Sitemap.RootNodes);
            foreach (var handle in pageHandles.Take(10)) // 限制数量
            {
                try
                {
                    var page = (RPPage)_document.LoadPackage(handle);
                    if (page == null) continue;
                    
                    var packageInfo = _document.GetPackageInfo(handle);
                    
                    _writer.WriteStartElement("Page");
                    _writer.WriteElementString("Name", packageInfo.PackageName);
                    _writer.WriteElementString("WidgetCount", page.Diagram?.Widgets.Count().ToString() ?? "0");
                    _writer.WriteEndElement();
                    
                    page.Dispose();
                }
                catch
                {
                    // 忽略错误，继续处理下一个
                }
            }
            _writer.WriteEndElement();
            
            _writer.WriteEndElement();
            _writer.WriteEndDocument();
        }

        private IEnumerable<RPPackageHandle> GetAllPackageHandles(IEnumerable<RPTreeMapNode> nodes)
        {
            foreach (var node in nodes)
            {
                if (node.NodeValue is RPPackageHandle handle)
                {
                    yield return handle;
                }
                
                foreach (var subHandle in GetAllPackageHandles(node.ChildNodes))
                {
                    yield return subHandle;
                }
            }
        }
    }
}
