#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import re
import struct
from pathlib import Path

def parse_axure_file(filename):
    """解析Axure RP文件的高级方法"""
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    print(f"正在分析Axure文件: {filename}")
    file_size = os.path.getsize(filename)
    print(f"文件大小: {file_size:,} 字节")
    
    try:
        with open(filename, 'rb') as f:
            # 读取文件头部
            header = f.read(100)
            print(f"文件头部: {header[:50].hex()}")
            
            # 分析文件结构
            analyze_file_structure(f, file_size)
            
    except Exception as e:
        print(f"解析文件时出错: {e}")

def analyze_file_structure(file_obj, file_size):
    """分析文件的内部结构"""
    
    file_obj.seek(0)
    
    # 尝试识别不同的数据块
    print("\n分析文件结构:")
    
    # 查找可能的字符串数据
    strings_found = []
    json_blocks = []
    
    chunk_size = 8192
    position = 0
    
    while position < file_size:
        file_obj.seek(position)
        chunk = file_obj.read(chunk_size)
        
        if not chunk:
            break
            
        # 查找JSON模式
        json_patterns = [
            rb'{"[^"]+":',  # JSON对象开始
            rb'\[{"[^"]+":',  # JSON数组开始
            rb'"[^"]+":{"',  # 嵌套JSON对象
        ]
        
        for pattern in json_patterns:
            matches = re.finditer(pattern, chunk)
            for match in matches:
                abs_pos = position + match.start()
                print(f"发现JSON模式在位置 {abs_pos}: {match.group()}")
                
                # 尝试提取完整的JSON块
                json_block = extract_json_block(file_obj, abs_pos)
                if json_block:
                    json_blocks.append((abs_pos, json_block))
        
        # 查找可读字符串
        strings_in_chunk = extract_strings_from_chunk(chunk, position)
        strings_found.extend(strings_in_chunk)
        
        position += chunk_size // 2  # 重叠读取以避免遗漏跨块的数据
    
    # 分析找到的字符串
    print(f"\n找到 {len(strings_found)} 个字符串:")
    unique_strings = list(set(s[1] for s in strings_found))
    unique_strings.sort(key=len, reverse=True)
    
    for i, string in enumerate(unique_strings[:20]):
        if len(string) > 5 and not string.isdigit():
            print(f"  {i+1}: {string[:80]}{'...' if len(string) > 80 else ''}")
    
    # 分析JSON块
    print(f"\n找到 {len(json_blocks)} 个JSON块:")
    for i, (pos, json_data) in enumerate(json_blocks[:5]):
        print(f"  块 {i+1} (位置 {pos}): {json_data[:100]}{'...' if len(json_data) > 100 else ''}")
        
        # 尝试解析JSON
        try:
            parsed = json.loads(json_data)
            print(f"    ✓ 成功解析JSON")
            if isinstance(parsed, dict):
                print(f"    包含键: {list(parsed.keys())[:5]}")
        except:
            print(f"    ✗ JSON解析失败")

def extract_json_block(file_obj, start_pos):
    """从指定位置提取完整的JSON块"""
    file_obj.seek(start_pos)
    
    # 读取足够的数据
    data = file_obj.read(10000)
    
    if not data:
        return None
    
    # 查找JSON的开始和结束
    brace_count = 0
    bracket_count = 0
    in_string = False
    escape_next = False
    json_start = -1
    json_end = -1
    
    for i, byte in enumerate(data):
        if byte > 127:  # 跳过非ASCII字符
            continue
            
        char = chr(byte)
        
        if escape_next:
            escape_next = False
            continue
            
        if char == '\\':
            escape_next = True
            continue
            
        if char == '"':
            in_string = not in_string
            continue
            
        if not in_string:
            if char == '{':
                if json_start == -1:
                    json_start = i
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0 and json_start != -1:
                    json_end = i + 1
                    break
            elif char == '[':
                if json_start == -1:
                    json_start = i
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
                if bracket_count == 0 and json_start != -1:
                    json_end = i + 1
                    break
    
    if json_start != -1 and json_end != -1:
        json_text = data[json_start:json_end].decode('utf-8', errors='ignore')
        # 清理控制字符
        json_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_text)
        return json_text
    
    return None

def extract_strings_from_chunk(chunk, offset):
    """从数据块中提取字符串"""
    strings = []
    current_string = b''
    start_pos = 0
    
    for i, byte in enumerate(chunk):
        if 32 <= byte <= 126:  # 可打印ASCII字符
            if not current_string:
                start_pos = i
            current_string += bytes([byte])
        else:
            if len(current_string) >= 5:  # 至少5个字符
                try:
                    decoded = current_string.decode('utf-8')
                    if decoded.strip() and not decoded.isdigit():
                        strings.append((offset + start_pos, decoded))
                except:
                    pass
            current_string = b''
    
    # 处理最后一个字符串
    if len(current_string) >= 5:
        try:
            decoded = current_string.decode('utf-8')
            if decoded.strip() and not decoded.isdigit():
                strings.append((offset + start_pos, decoded))
        except:
            pass
    
    return strings

def search_for_axure_metadata(filename):
    """搜索Axure特定的元数据"""
    print("\n搜索Axure元数据:")
    
    # Axure相关的关键词
    keywords = [
        b'page', b'Page', b'PAGE',
        b'widget', b'Widget', b'WIDGET',
        b'master', b'Master', b'MASTER',
        b'prototype', b'Prototype', b'PROTOTYPE',
        b'axure', b'Axure', b'AXURE',
        b'diagram', b'Diagram', b'DIAGRAM',
        b'flow', b'Flow', b'FLOW',
        b'interaction', b'Interaction', b'INTERACTION'
    ]
    
    with open(filename, 'rb') as f:
        content = f.read()
        
        for keyword in keywords:
            positions = []
            start = 0
            while True:
                pos = content.find(keyword, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
                if len(positions) >= 10:  # 限制显示数量
                    break
            
            if positions:
                print(f"  '{keyword.decode()}': 找到 {len(positions)} 次")
                # 显示前几个位置的上下文
                for pos in positions[:3]:
                    context_start = max(0, pos - 20)
                    context_end = min(len(content), pos + len(keyword) + 20)
                    context = content[context_start:context_end]
                    # 替换不可打印字符
                    context_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    print(f"    位置 {pos}: ...{context_str}...")

if __name__ == "__main__":
    filename = "完整性(3)_recovered.rp"
    parse_axure_file(filename)
    search_for_axure_metadata(filename)
