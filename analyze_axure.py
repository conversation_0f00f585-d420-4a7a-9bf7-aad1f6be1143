#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def analyze_axure_file(filename):
    """分析Axure RP文件"""
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    # 获取文件基本信息
    size = os.path.getsize(filename)
    print(f"文件名: {filename}")
    print(f"文件大小: {size:,} 字节 ({size/1024/1024:.2f} MB)")
    
    # 读取文件头部
    with open(filename, 'rb') as f:
        header = f.read(100)
        print(f"文件头部 (前20字节): {header[:20].hex()}")
        
        # 检查是否是ZIP格式
        if header.startswith(b'PK'):
            print("✓ 这是一个ZIP压缩文件")
            analyze_zip_content(filename)
        else:
            print("✗ 这不是标准的ZIP文件")
            # 尝试查找可能的文本内容
            try_extract_text_content(filename)

def analyze_zip_content(filename):
    """分析ZIP文件内容"""
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            file_list = zip_file.namelist()
            print(f"\nZIP文件包含 {len(file_list)} 个文件:")
            
            for file_name in file_list[:20]:  # 只显示前20个文件
                file_info = zip_file.getinfo(file_name)
                print(f"  - {file_name} ({file_info.file_size} 字节)")
            
            if len(file_list) > 20:
                print(f"  ... 还有 {len(file_list) - 20} 个文件")
            
            # 尝试读取一些关键文件
            key_files = ['document.xml', 'settings.xml', 'data.xml', 'project.xml']
            for key_file in key_files:
                if key_file in file_list:
                    print(f"\n正在分析 {key_file}:")
                    try:
                        content = zip_file.read(key_file)
                        if content.startswith(b'<?xml'):
                            # 这是XML文件
                            try:
                                root = ET.fromstring(content)
                                print(f"  XML根元素: {root.tag}")
                                print(f"  XML属性: {root.attrib}")
                                # 显示前几个子元素
                                for i, child in enumerate(root[:5]):
                                    print(f"    子元素 {i+1}: {child.tag}")
                            except ET.ParseError as e:
                                print(f"  XML解析错误: {e}")
                        else:
                            print(f"  文件内容 (前100字符): {content[:100]}")
                    except Exception as e:
                        print(f"  读取文件错误: {e}")
                        
    except zipfile.BadZipFile:
        print("错误: 文件不是有效的ZIP文件")
    except Exception as e:
        print(f"分析ZIP文件时出错: {e}")

def try_extract_text_content(filename):
    """尝试从非ZIP文件中提取文本内容"""
    try:
        with open(filename, 'rb') as f:
            # 读取更大的文件部分来分析
            file_size = os.path.getsize(filename)
            chunk_size = min(100000, file_size)  # 读取前100KB或整个文件
            content = f.read(chunk_size)

            print(f"\n分析文件内容 (前 {len(content)} 字节):")

            # 分析文件头部结构
            print("文件头部分析:")
            header = content[:100]
            for i in range(0, min(100, len(header)), 16):
                hex_part = ' '.join(f'{b:02x}' for b in header[i:i+16])
                ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in header[i:i+16])
                print(f"  {i:04x}: {hex_part:<48} {ascii_part}")

            # 尝试查找XML标签
            xml_patterns = [b'<?xml', b'<xml', b'<root', b'<document', b'<project']
            for pattern in xml_patterns:
                if pattern in content:
                    print(f"\n发现XML模式: {pattern}")
                    xml_start = content.find(pattern)
                    xml_end = content.find(b'>', xml_start) + 1
                    if xml_end > xml_start:
                        xml_tag = content[xml_start:xml_end]
                        print(f"  XML标签: {xml_tag}")

            # 查找可能的字符串内容
            print("\n查找可能的文本字符串:")
            strings_found = []
            current_string = b''

            for byte in content:
                if 32 <= byte <= 126:  # 可打印ASCII字符
                    current_string += bytes([byte])
                else:
                    if len(current_string) >= 5:  # 至少5个字符的字符串
                        try:
                            decoded = current_string.decode('utf-8')
                            if decoded.strip() and not decoded.isdigit():
                                strings_found.append(decoded)
                        except:
                            pass
                    current_string = b''

            # 显示找到的字符串
            unique_strings = list(set(strings_found))
            unique_strings.sort(key=len, reverse=True)

            print(f"找到 {len(unique_strings)} 个唯一字符串:")
            for i, string in enumerate(unique_strings[:20]):  # 显示前20个最长的字符串
                if len(string.strip()) > 3:
                    print(f"  {i+1}: {string[:80]}{'...' if len(string) > 80 else ''}")

            # 尝试查找特定的Axure相关关键词
            axure_keywords = [b'axure', b'Axure', b'AXURE', b'widget', b'page', b'master', b'prototype']
            print("\n查找Axure相关关键词:")
            for keyword in axure_keywords:
                count = content.lower().count(keyword.lower())
                if count > 0:
                    print(f"  '{keyword.decode()}': 出现 {count} 次")

    except Exception as e:
        print(f"提取文本内容时出错: {e}")

if __name__ == "__main__":
    filename = "完整性(3)_recovered.rp"
    analyze_axure_file(filename)
