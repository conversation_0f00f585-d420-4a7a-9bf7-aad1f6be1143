#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import re
from pathlib import Path

def extract_json_from_axure(filename):
    """从Axure文件中提取JSON数据"""
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    print(f"正在分析文件: {filename}")
    
    try:
        with open(filename, 'rb') as f:
            # 读取文件内容
            content = f.read()
            
            # 查找JSON数据的开始位置
            # 从文件头部分析中我们看到有 {"root":{"children 这样的结构
            json_start_pattern = b'{"root":'
            json_start = content.find(json_start_pattern)
            
            if json_start == -1:
                print("未找到JSON数据开始标记")
                return
            
            print(f"找到JSON数据开始位置: {json_start}")
            
            # 从JSON开始位置提取数据
            json_data = content[json_start:]
            
            # 尝试找到JSON数据的结束位置
            # 我们需要找到匹配的大括号
            brace_count = 0
            json_end = 0
            in_string = False
            escape_next = False
            
            for i, byte in enumerate(json_data):
                char = chr(byte) if byte < 128 else None
                
                if char is None:
                    continue
                    
                if escape_next:
                    escape_next = False
                    continue
                    
                if char == '\\':
                    escape_next = True
                    continue
                    
                if char == '"':
                    in_string = not in_string
                    continue
                    
                if not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
            
            if json_end == 0:
                print("未找到JSON数据结束位置，尝试解析前10KB")
                json_text = json_data[:10240].decode('utf-8', errors='ignore')
            else:
                print(f"找到JSON数据结束位置: {json_end}")
                json_text = json_data[:json_end].decode('utf-8', errors='ignore')
            
            # 尝试解析JSON
            try:
                parsed_json = json.loads(json_text)
                print("✓ 成功解析JSON数据")
                analyze_json_structure(parsed_json)
                
                # 保存解析后的JSON到文件
                output_file = "axure_data.json"
                with open(output_file, 'w', encoding='utf-8') as out_f:
                    json.dump(parsed_json, out_f, indent=2, ensure_ascii=False)
                print(f"✓ JSON数据已保存到: {output_file}")
                
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print("尝试修复JSON数据...")
                
                # 尝试一些简单的修复
                fixed_json = try_fix_json(json_text)
                if fixed_json:
                    try:
                        parsed_json = json.loads(fixed_json)
                        print("✓ 修复后成功解析JSON数据")
                        analyze_json_structure(parsed_json)
                    except:
                        print("修复后仍无法解析JSON")
                        # 显示JSON文本的前几行
                        lines = json_text.split('\n')[:20]
                        print("JSON文本前20行:")
                        for i, line in enumerate(lines, 1):
                            print(f"  {i:2d}: {line[:100]}{'...' if len(line) > 100 else ''}")
                
    except Exception as e:
        print(f"处理文件时出错: {e}")

def try_fix_json(json_text):
    """尝试修复损坏的JSON"""
    # 移除可能的非JSON字符
    # 查找第一个 { 和最后一个 }
    first_brace = json_text.find('{')
    last_brace = json_text.rfind('}')
    
    if first_brace != -1 and last_brace != -1 and last_brace > first_brace:
        return json_text[first_brace:last_brace+1]
    
    return None

def analyze_json_structure(data, prefix="", max_depth=3, current_depth=0):
    """分析JSON数据结构"""
    if current_depth >= max_depth:
        return
        
    if isinstance(data, dict):
        print(f"{prefix}字典 (包含 {len(data)} 个键):")
        for key, value in list(data.items())[:10]:  # 只显示前10个键
            print(f"{prefix}  - {key}: {type(value).__name__}")
            if isinstance(value, (dict, list)) and current_depth < max_depth - 1:
                analyze_json_structure(value, prefix + "    ", max_depth, current_depth + 1)
        if len(data) > 10:
            print(f"{prefix}  ... 还有 {len(data) - 10} 个键")
            
    elif isinstance(data, list):
        print(f"{prefix}列表 (包含 {len(data)} 个元素):")
        if data:
            first_item = data[0]
            print(f"{prefix}  - 第一个元素类型: {type(first_item).__name__}")
            if isinstance(first_item, (dict, list)) and current_depth < max_depth - 1:
                analyze_json_structure(first_item, prefix + "    ", max_depth, current_depth + 1)
    else:
        print(f"{prefix}值: {str(data)[:100]}{'...' if len(str(data)) > 100 else ''}")

if __name__ == "__main__":
    filename = "完整性(3)_recovered.rp"
    extract_json_from_axure(filename)
